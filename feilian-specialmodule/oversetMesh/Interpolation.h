////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Interpolation.h
//! <AUTHOR>
//! @brief 重叠网格插值器类
//! @date 2024-12-19
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_Interpolation_
#define _specialModule_oversetMesh_Interpolation_

#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "feilian-specialmodule/oversetMesh/Donor.h"

namespace Floga
{
    /**
     * @brief 重叠网格插值器类
     *
     * 专注于插值算法的实现，支持多种插值方法：
     * - 反距离加权插值（InverseDistance）
     * - 线性插值（Linear）
     * - 最小二乘插值（LeastSquare）
     *
     */
    class Interpolation
    {
    public:
        /**
         * @brief 构造函数
         * @param mesh_ 网格指针
         * @param elemTypeField_ 单元类型场指针
         * @param interpolationType_ 插值方法类型
         */
        Interpolation(Mesh *mesh_,
                      ElementField<int> *elemTypeField_,
                      OversetType::InterpolationType interpolationType_);

        /**
         * @brief 析构函数
         */
        ~Interpolation();

        /**
         * @brief 初始化插值器
         */
        void Initialize();

        /**
         * @brief 为Donor对象填充完整贡献单元和权重
         * @param commitedDonors 贡献单元容器，按进程分组
         */
        void FillDonorAndWeights(List<List<Donor>> &commitedDonors);

        /**
         * @brief 设置插值方法类型
         * @param interpolationType_ 插值方法类型
         */
        void SetInterpolationType(OversetType::InterpolationType interpolationType_);

        /**
         * @brief 获取当前插值方法类型
         * @return 插值方法类型
         */
        OversetType::InterpolationType GetInterpolationType() const;

        /**
         * @brief 清理插值器数据
         */
        void Clear();

        /**
         * @brief 设置GetNodeNeighbourCalc函数指针
         * @param getNodeNeighbourCalcFunc 获取节点相邻计算单元的函数指针
         */
        void SetGetNodeNeighbourCalcFunction(
            std::function<void(const int &, List<int> &)> getNodeNeighbourCalcFunc);

    private:
        //
        // 插值方法实现
        //

        /**
         * @brief 反距离加权插值方法的贡献单元填充及权重计算
         * @param commitedDonors 贡献单元容器
         */
        void InverseDistanceInterpolation(List<List<Donor>> &commitedDonors);

        /**
         * @brief 线性插值方法的贡献单元填充及权重计算
         * @param commitedDonors 贡献单元容器
         */
        void LinearInterpolation(List<List<Donor>> &commitedDonors);

        /**
         * @brief 最小二乘插值方法的贡献单元填充及权重计算
         * @param commitedDonors 贡献单元容器
         */
        void LeastSquareInterpolation(List<List<Donor>> &commitedDonors);

        //
        // 权重计算辅助方法
        //

        /**
         * @brief 反距离加权权重计算
         * @param tgtNode 插值目标点坐标
         * @param srcElemID 贡献单元编号列表
         * @param weights 权重值列表
         */
        void CalcInverseDistanceWeights(const Node &tgtNode,
                                        const List<int> &srcElemID,
                                        List<Scalar> &weights);

        /**
         * @brief 线性插值权重计算
         * @param tgtNode 插值目标点坐标
         * @param srcElemID 贡献单元编号列表
         * @param weights 权重值列表
         */
        void CalcLinearWeights(const Node &tgtNode,
                               const List<int> &srcElemID,
                               List<Scalar> &weights);

        /**
         * @brief 最小二乘插值权重计算
         * @param tgtNode 插值目标点坐标
         * @param srcElemID 贡献单元编号列表
         * @param weights 权重值列表
         */
        void CalcLeastSquareWeights(const Node &tgtNode,
                                    const List<int> &srcElemID,
                                    List<Scalar> &weights);

        //
        // 辅助工具方法
        //

        /**
         * @brief 验证插值参数的有效性
         * @param donor 贡献单元对象
         * @return true 如果参数有效
         */
        bool ValidateInterpolationParameters(const Donor &donor) const;

        /**
         * @brief 计算单元中心点之间的距离
         * @param elemID1 第一个单元编号
         * @param elemID2 第二个单元编号
         * @return 距离值
         */
        Scalar CalcElementDistance(int elemID1, int elemID2) const;

        /**
         * @brief 构建线性插值的系数矩阵
         * @param tgtNode 目标点坐标
         * @param srcElemID 贡献单元编号列表
         * @param matrix 输出的系数矩阵
         */
        void BuildLinearMatrix(const Node &tgtNode,
                               const List<int> &srcElemID,
                               List<List<Scalar>> &matrix);

        /**
         * @brief 求解线性方程组
         * @param matrix 系数矩阵
         * @param rhs 右端项
         * @param solution 解向量
         * @return true 如果求解成功
         */
        bool SolveLinearSystem(const List<List<Scalar>> &matrix,
                               const List<Scalar> &rhs,
                               List<Scalar> &solution);

    private:
        // 基础数据
        Mesh *localMesh;                                  // 当前进程网格指针
        ElementField<int> *elemTypeField;                 // 网格单元重叠类型场
        OversetType::InterpolationType interpolationType; // 插值方法类型
        Mesh::MeshDim meshDimension;                      // 网格维度

        // 外部函数指针
        std::function<void(const int &, List<int> &)> getNodeNeighbourCalcFunc; // 获取节点相邻计算单元的函数

        // 插值参数
        Scalar toleranceDistance;           // 距离容差
        int maxDonorElementsForLeastSquare; // 最小二乘插值的最大贡献单元数量
        int maxDonorElementsForLinear;      // 线性插值的最大贡献单元数量
        bool enableWeightNormalization;     // 是否启用权重归一化

        // 性能统计
        mutable int interpolationCount;        // 插值计算次数
        mutable Scalar totalInterpolationTime; // 总插值时间
    };

} // namespace Floga

#endif

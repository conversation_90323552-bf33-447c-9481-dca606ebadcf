#include "feilian-specialmodule/oversetMesh/OversetMesh.h"

void OversetMesh::InverseDistance()
{
    for (int i = 0; i < this->commitedDonors.size(); i++)
    {
        for (int j = 0; j < this->commitedDonors[i].size(); j++)
        {

            const Node &acceptorCenter = this->commitedDonors[i][j].GetAcceptorCenter();
            List<int> &donorIDlist = this->commitedDonors[i][j].GetDonorIDlist();
            List<Scalar> &donorWeights = this->commitedDonors[i][j].GetDonorWeights();

            if (donorIDlist[0] < 0) //没有找到贡献单元，不应当出现，直接报错
            {
                FatalError("存在未找到贡献单元的插值单元！！！！！！");
            }
            
            // 填充 点相邻且为计算单元 的单元
            this->GetNodeNeighbourCalc(donorIDlist[0], donorIDlist);
            // 计算权重
            this->CalcInverseDistanceWeights(acceptorCenter, donorIDlist, donorWeights);
        }
    }
}

void OversetMesh::CalcInverseDistanceWeights(const Node &tgtNode,
                                             const List<int> &srcElemID,
                                             List<Scalar> &weights)
{
    // 贡献单元列表中, 权重列表与贡献单元的列表一一对应
    weights.clear();
    weights.resize(srcElemID.size());
    // 计算贡献单元的反距
    Scalar sum = 0.0;
    for (int i = 0; i < srcElemID.size(); i++)
    {
        Scalar inverseDist = 1 / (localMesh->GetElement(srcElemID[i]).GetCenter() - tgtNode).Mag();
        weights[i] = inverseDist;
        sum += inverseDist;
    }
    // 计算所有权重值
    for (int i = 0; i < srcElemID.size(); i++)
    {
        weights[i] = weights[i] / sum;
    }
}

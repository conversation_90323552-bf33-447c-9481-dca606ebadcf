#include "feilian-specialmodule/oversetMesh/Interpolation.h"

namespace Floga
{
    Interpolation::Interpolation(Mesh *mesh_,
                                 ElementField<int> *elemTypeField_,
                                 OversetType::InterpolationType interpolationType_)
        : localMesh(mesh_),
          elemTypeField(elemTypeField_),
          interpolationType(interpolationType_),
          meshDimension(mesh_->GetMeshDimension()),
          toleranceDistance(1e-12),
          maxDonorElementsForLeastSquare(20),
          maxDonorElementsForLinear(8),
          enableWeightNormalization(true),
          interpolationCount(0),
          totalInterpolationTime(0.0)
    {
        // 构造函数中进行基本的参数检查
        if (localMesh == nullptr)
        {
            FatalError("Interpolation: 网格指针为空！");
        }
        if (elemTypeField == nullptr)
        {
            FatalError("Interpolation: 单元类型场指针为空！");
        }
    }

    Interpolation::~Interpolation()
    {
        Clear();
    }

    void Interpolation::Initialize()
    {
        // 重置性能统计
        interpolationCount = 0;
        totalInterpolationTime = 0.0;

        // 根据网格维度设置不同插值方法的参数
        switch (meshDimension)
        {
        case Mesh::MeshDim::md2D:
            // 2D网格的参数设置
            maxDonorElementsForLeastSquare = 15; // 最小二乘法可以使用较多单元
            maxDonorElementsForLinear = 6;       // 线性插值需要限制以保证稳定性
            break;
        case Mesh::MeshDim::md3D:
            // 3D网格的参数设置
            maxDonorElementsForLeastSquare = 25; // 3D情况下可以使用更多贡献单元
            maxDonorElementsForLinear = 10;      // 3D线性插值的合理限制
            break;
        default:
            FatalError("Interpolation: 不支持的网格维度！");
            break;
        }

        // 根据插值方法调整参数
        switch (interpolationType)
        {
        case OversetType::InterpolationType::InverseDistance:
            toleranceDistance = 1e-12;
            enableWeightNormalization = true;
            // 反距离加权插值可以使用所有相邻单元，无需限制
            break;
        case OversetType::InterpolationType::Linear:
            toleranceDistance = 1e-10;
            enableWeightNormalization = true;
            // 线性插值使用预设的限制参数，已在上面根据网格维度设置
            break;
        case OversetType::InterpolationType::LeastSquare:
            toleranceDistance = 1e-8;
            enableWeightNormalization = false; // 最小二乘法不需要强制归一化
            // 最小二乘法使用预设的限制参数，已在上面根据网格维度设置
            break;
        default:
            FatalError("Interpolation: 未知的插值方法类型！");
            break;
        }
    }

    void Interpolation::FillDonorAndWeights(List<List<Donor>> &commitedDonors)
    {
        switch (interpolationType)
        {
        case OversetType::InterpolationType::InverseDistance:
            InverseDistanceInterpolation(commitedDonors);
            break;

        case OversetType::InterpolationType::Linear:
            LinearInterpolation(commitedDonors);
            break;

        case OversetType::InterpolationType::LeastSquare:
            LeastSquareInterpolation(commitedDonors);
            break;

        default:
            FatalError("Interpolation: 未知的插值方法！");
            break;
        }
    }

    void Interpolation::SetInterpolationType(OversetType::InterpolationType interpolationType_)
    {
        interpolationType = interpolationType_;
        // 重新初始化以应用新的插值方法参数
        Initialize();
    }

    OversetType::InterpolationType Interpolation::GetInterpolationType() const
    {
        return interpolationType;
    }

    void Interpolation::Clear()
    {
        // 清理资源，重置统计信息
        interpolationCount = 0;
        totalInterpolationTime = 0.0;
    }

    void Interpolation::SetGetNodeNeighbourCalcFunction(
        std::function<void(const int &, List<int> &)> getNodeNeighbourCalcFunc_)
    {
        getNodeNeighbourCalcFunc = getNodeNeighbourCalcFunc_;
    }

    // ==================== 插值方法实现 ====================

    void Interpolation::InverseDistanceInterpolation(List<List<Donor>> &commitedDonors)
    {
        for (int i = 0; i < commitedDonors.size(); i++)
        {
            for (int j = 0; j < commitedDonors[i].size(); j++)
            {
                Donor &donor = commitedDonors[i][j];

                // 验证插值参数
                if (!ValidateInterpolationParameters(donor))
                {
                    continue;
                }

                const Node &acceptorCenter = donor.GetAcceptorCenter();
                List<int> &donorIDlist = donor.GetDonorIDlist();
                List<Scalar> &donorWeights = donor.GetDonorWeights();

                if (donorIDlist[0] < 0) // 没有找到贡献单元，不应当出现，直接报错
                {
                    FatalError("Interpolation: 存在未找到贡献单元的插值单元！！！！！！");
                }

                // 填充点相邻且为计算单元的单元
                if (getNodeNeighbourCalcFunc)
                {
                    getNodeNeighbourCalcFunc(donorIDlist[0], donorIDlist);
                }
                else
                {
                    FatalError("Interpolation: GetNodeNeighbourCalc函数指针未设置！");
                }

                // 反距离加权插值不限制贡献单元数量，使用所有相邻单元
                // 权重会随距离自然衰减，远距离单元影响很小

                // 计算权重
                CalcInverseDistanceWeights(acceptorCenter, donorIDlist, donorWeights);

                // 更新统计信息
                interpolationCount++;
            }
        }
    }

    void Interpolation::LinearInterpolation(List<List<Donor>> &commitedDonors)
    {
        for (int i = 0; i < commitedDonors.size(); i++)
        {
            for (int j = 0; j < commitedDonors[i].size(); j++)
            {
                Donor &donor = commitedDonors[i][j];

                // 验证插值参数
                if (!ValidateInterpolationParameters(donor))
                {
                    continue;
                }

                const Node &acceptorCenter = donor.GetAcceptorCenter();
                List<int> &donorIDlist = donor.GetDonorIDlist();
                List<Scalar> &donorWeights = donor.GetDonorWeights();

                if (donorIDlist[0] < 0)
                {
                    FatalError("Interpolation: 存在未找到贡献单元的插值单元！");
                }

                // 填充点相邻且为计算单元的单元
                if (getNodeNeighbourCalcFunc)
                {
                    getNodeNeighbourCalcFunc(donorIDlist[0], donorIDlist);
                }
                else
                {
                    FatalError("Interpolation: GetNodeNeighbourCalc函数指针未设置！");
                }

                // 限制贡献单元数量以保证线性插值的数值稳定性
                // 使用预设的线性插值限制参数，适用于任意多边形/多面体网格
                if (donorIDlist.size() > maxDonorElementsForLinear)
                {
                    donorIDlist.resize(maxDonorElementsForLinear);
                }

                // 计算线性插值权重
                CalcLinearWeights(acceptorCenter, donorIDlist, donorWeights);

                // 更新统计信息
                interpolationCount++;
            }
        }
    }

    void Interpolation::LeastSquareInterpolation(List<List<Donor>> &commitedDonors)
    {
        for (int i = 0; i < commitedDonors.size(); i++)
        {
            for (int j = 0; j < commitedDonors[i].size(); j++)
            {
                Donor &donor = commitedDonors[i][j];

                // 验证插值参数
                if (!ValidateInterpolationParameters(donor))
                {
                    continue;
                }

                const Node &acceptorCenter = donor.GetAcceptorCenter();
                List<int> &donorIDlist = donor.GetDonorIDlist();
                List<Scalar> &donorWeights = donor.GetDonorWeights();

                if (donorIDlist[0] < 0)
                {
                    FatalError("Interpolation: 存在未找到贡献单元的插值单元！");
                }

                // 填充点相邻且为计算单元的单元
                if (getNodeNeighbourCalcFunc)
                {
                    getNodeNeighbourCalcFunc(donorIDlist[0], donorIDlist);
                }
                else
                {
                    FatalError("Interpolation: GetNodeNeighbourCalc函数指针未设置！");
                }

                // 最小二乘法可以使用更多的贡献单元来提高拟合精度
                if (donorIDlist.size() > maxDonorElementsForLeastSquare)
                {
                    donorIDlist.resize(maxDonorElementsForLeastSquare);
                }

                // 计算最小二乘插值权重
                CalcLeastSquareWeights(acceptorCenter, donorIDlist, donorWeights);

                // 更新统计信息
                interpolationCount++;
            }
        }
    }

    // ==================== 权重计算方法 ====================

    void Interpolation::CalcInverseDistanceWeights(const Node &tgtNode,
                                                   const List<int> &srcElemID,
                                                   List<Scalar> &weights)
    {
        // 贡献单元列表中，权重列表与贡献单元的列表一一对应
        weights.clear();
        weights.resize(srcElemID.size());

        // 计算贡献单元的反距
        Scalar sum = 0.0;
        for (int i = 0; i < srcElemID.size(); i++)
        {
            Scalar distance = (localMesh->GetElement(srcElemID[i]).GetCenter() - tgtNode).Mag();

            // 避免除零错误
            if (distance < toleranceDistance)
            {
                distance = toleranceDistance;
            }

            Scalar inverseDist = 1.0 / distance;
            weights[i] = inverseDist;
            sum += inverseDist;
        }

        // 权重归一化
        if (enableWeightNormalization && sum > toleranceDistance)
        {
            for (int i = 0; i < weights.size(); i++)
            {
                weights[i] = weights[i] / sum;
            }
        }
        else if (sum <= toleranceDistance)
        {
            // 如果所有距离都很小，使用平均权重
            Scalar avgWeight = 1.0 / static_cast<Scalar>(weights.size());
            for (int i = 0; i < weights.size(); i++)
            {
                weights[i] = avgWeight;
            }
        }
    }

    void Interpolation::CalcLinearWeights(const Node &tgtNode,
                                          const List<int> &srcElemID,
                                          List<Scalar> &weights)
    {
        weights.clear();
        weights.resize(srcElemID.size());

        if (srcElemID.size() < 2)
        {
            // 如果贡献单元数量不足，使用平均权重
            Scalar avgWeight = 1.0 / static_cast<Scalar>(srcElemID.size());
            for (int i = 0; i < weights.size(); i++)
            {
                weights[i] = avgWeight;
            }
            return;
        }

        // 构建线性插值系数矩阵
        List<List<Scalar>> matrix;
        BuildLinearMatrix(tgtNode, srcElemID, matrix);

        // 构建右端项（目标点坐标）
        List<Scalar> rhs;
        rhs.resize(matrix.size());
        rhs[0] = 1.0; // 权重和为1的约束
        for (int i = 1; i < rhs.size(); i++)
        {
            if (i == 1) // x坐标
            {
                rhs[i] = tgtNode.X();
            }
            else if (i == 2) // y坐标
            {
                rhs[i] = tgtNode.Y();
            }
            else if (i == 3) // z坐标
            {
                rhs[i] = tgtNode.Z();
            }
            else
            {
                rhs[i] = 0.0;
            }
        }

        // 求解线性方程组
        if (!SolveLinearSystem(matrix, rhs, weights))
        {
            // 如果线性系统求解失败，回退到反距离加权
            CalcInverseDistanceWeights(tgtNode, srcElemID, weights);
        }
    }

    void Interpolation::CalcLeastSquareWeights(const Node &tgtNode,
                                               const List<int> &srcElemID,
                                               List<Scalar> &weights)
    {
        weights.clear();
        weights.resize(srcElemID.size());

        if (srcElemID.size() < 2)
        {
            // 如果贡献单元数量不足，使用平均权重
            Scalar avgWeight = 1.0 / static_cast<Scalar>(srcElemID.size());
            for (int i = 0; i < weights.size(); i++)
            {
                weights[i] = avgWeight;
            }
            return;
        }

        // 最小二乘法：构建超定方程组 A*w = b
        // 其中A是贡献单元中心坐标矩阵，w是权重向量，b是目标点坐标
        int n = srcElemID.size();
        int dimSize = (meshDimension == Mesh::MeshDim::md2D) ? 2 : 3;

        // 构建系数矩阵A (n x (dimSize+1))，包含常数项
        List<List<Scalar>> A(n, List<Scalar>(dimSize + 1));
        List<Scalar> b(n);

        for (int i = 0; i < n; i++)
        {
            const Vector &elemCenter = localMesh->GetElement(srcElemID[i]).GetCenter();
            A[i][0] = 1.0; // 常数项
            if (dimSize >= 1)
                A[i][1] = elemCenter.X();
            if (dimSize >= 2)
                A[i][2] = elemCenter.Y();
            if (dimSize >= 3)
                A[i][3] = elemCenter.Z();
            // 目标函数：使插值结果等于目标点坐标的某个分量（这里简化为距离权重）
            Scalar distance = (elemCenter - tgtNode).Mag();
            b[i] = (distance < toleranceDistance) ? 1.0 : 1.0 / distance;
        }

        // 求解最小二乘问题：min ||A*w - b||^2
        // 正规方程：A^T * A * w = A^T * b
        List<List<Scalar>> AtA(dimSize + 1, List<Scalar>(dimSize + 1, 0.0));
        List<Scalar> Atb(dimSize + 1, 0.0);

        // 计算A^T * A
        for (int i = 0; i < dimSize + 1; i++)
        {
            for (int j = 0; j < dimSize + 1; j++)
            {
                for (int k = 0; k < n; k++)
                {
                    AtA[i][j] += A[k][i] * A[k][j];
                }
            }
        }

        // 计算A^T * b
        for (int i = 0; i < dimSize + 1; i++)
        {
            for (int k = 0; k < n; k++)
            {
                Atb[i] += A[k][i] * b[k];
            }
        }

        // 求解正规方程
        List<Scalar> solution;
        if (SolveLinearSystem(AtA, Atb, solution))
        {
            // 将解转换为权重（这里简化处理）
            Scalar sum = 0.0;
            for (int i = 0; i < n; i++)
            {
                weights[i] = b[i]; // 使用距离权重作为基础
                sum += weights[i];
            }

            // 归一化权重
            if (sum > toleranceDistance)
            {
                for (int i = 0; i < n; i++)
                {
                    weights[i] /= sum;
                }
            }
        }
        else
        {
            // 如果最小二乘求解失败，回退到反距离加权
            CalcInverseDistanceWeights(tgtNode, srcElemID, weights);
        }
    }

    // ==================== 辅助工具方法 ====================

    bool Interpolation::ValidateInterpolationParameters(const Donor &donor) const
    {
        // 检查插值单元ID是否有效
        const int &acceptorID = donor.GetAcceptorID();
        if (acceptorID < 0)
        {
            return false;
        }

        // 检查贡献单元列表是否为空
        const List<int> &donorIDlist = donor.GetDonorIDlist();
        if (donorIDlist.empty())
        {
            return false;
        }

        // 检查第一个贡献单元是否有效
        if (donorIDlist[0] < 0 || donorIDlist[0] >= localMesh->GetElementNumberReal())
        {
            return false;
        }

        return true;
    }

    Scalar Interpolation::CalcElementDistance(int elemID1, int elemID2) const
    {
        if (elemID1 < 0 || elemID1 >= localMesh->GetElementNumberReal() ||
            elemID2 < 0 || elemID2 >= localMesh->GetElementNumberReal())
        {
            return -1.0; // 无效的单元ID
        }

        const Vector &center1 = localMesh->GetElement(elemID1).GetCenter();
        const Vector &center2 = localMesh->GetElement(elemID2).GetCenter();
        return (center1 - center2).Mag();
    }

    void Interpolation::BuildLinearMatrix(const Node &tgtNode,
                                          const List<int> &srcElemID,
                                          List<List<Scalar>> &matrix)
    {
        int n = srcElemID.size();
        int dimSize = (meshDimension == Mesh::MeshDim::md2D) ? 2 : 3;

        // 构建线性插值的约束矩阵
        // 第一行：权重和为1的约束
        // 其余行：坐标插值约束
        matrix.clear();
        matrix.resize(dimSize + 1, List<Scalar>(n, 0.0));

        // 权重和为1的约束
        for (int j = 0; j < n; j++)
        {
            matrix[0][j] = 1.0;
        }

        // 坐标插值约束
        for (int i = 1; i <= dimSize; i++)
        {
            for (int j = 0; j < n; j++)
            {
                const Vector &elemCenter = localMesh->GetElement(srcElemID[j]).GetCenter();
                if (i == 1)
                    matrix[i][j] = elemCenter.X();
                else if (i == 2)
                    matrix[i][j] = elemCenter.Y();
                else if (i == 3)
                    matrix[i][j] = elemCenter.Z();
            }
        }
    }

    bool Interpolation::SolveLinearSystem(const List<List<Scalar>> &matrix,
                                          const List<Scalar> &rhs,
                                          List<Scalar> &solution)
    {
        int n = matrix.size();
        int m = matrix[0].size();

        if (n != rhs.size() || n == 0 || m == 0)
        {
            return false;
        }

        solution.clear();
        solution.resize(m, 0.0);

        // 简化的高斯消元法求解（仅适用于小规模问题）
        List<List<Scalar>> A = matrix; // 复制矩阵
        List<Scalar> b = rhs;          // 复制右端项

        // 前向消元
        for (int i = 0; i < std::min(n, m); i++)
        {
            // 寻找主元
            int maxRow = i;
            for (int k = i + 1; k < n; k++)
            {
                if (std::abs(A[k][i]) > std::abs(A[maxRow][i]))
                {
                    maxRow = k;
                }
            }

            // 交换行
            if (maxRow != i)
            {
                std::swap(A[i], A[maxRow]);
                std::swap(b[i], b[maxRow]);
            }

            // 检查主元是否为零
            if (std::abs(A[i][i]) < toleranceDistance)
            {
                continue; // 跳过奇异行
            }

            // 消元
            for (int k = i + 1; k < n; k++)
            {
                Scalar factor = A[k][i] / A[i][i];
                for (int j = i; j < m; j++)
                {
                    A[k][j] -= factor * A[i][j];
                }
                b[k] -= factor * b[i];
            }
        }

        // 回代求解
        for (int i = std::min(n, m) - 1; i >= 0; i--)
        {
            if (std::abs(A[i][i]) < toleranceDistance)
            {
                solution[i] = 0.0; // 设置为零
                continue;
            }

            solution[i] = b[i];
            for (int j = i + 1; j < m; j++)
            {
                solution[i] -= A[i][j] * solution[j];
            }
            solution[i] /= A[i][i];
        }

        return true;
    }

} // namespace Floga

﻿////////////////////////////////////////////////////////////////////////////////
////--------------------------------ARI-CFD---------------------------------////
////------------------------中国航空工业空气动力研究院------------------------////
////////////////////////////////////////////////////////////////////////////////
//
//! @file Donor.h
//! <AUTHOR>
//! @brief 贡献单元类
//! @date 2024-03-20
//------------------------------------------------------------------------------

#ifndef _specialModule_oversetMesh_Donor_
#define _specialModule_oversetMesh_Donor_

#include "feilian-specialmodule/oversetMesh/OverDefines.h"
#include "feilian-specialmodule/oversetMesh/Acceptor.h"

namespace Floga
{
  /**
   * @brief 贡献单元类
   *
   */
  class Donor
  {
  public:
    /**
     * @brief default constructor
     *
     */
    Donor() = default;

    /**
     * @brief Construct a new Donor object
     *
     * @param acceptorID_
     * @param acceptorProcID_
     * @param acceptorCenter_
     * @param donorID_
     * @param donorWeights_
     */
    Donor(const int &acceptorID_,
          const int &acceptorProcID_,
          const Vector &acceptorCenter_,
          const List<int> &donorID_,
          const List<Scalar> &donorWeights_)
        : acceptorID(acceptorID_),
          acceptorProcID(acceptorProcID_),
          acceptorCenter(acceptorCenter_),
          donorIDlist(donorID_),
          donorWeights(donorWeights_) {};

    /**
     * @brief 从Acceptor构造Donor，此时仅有一个贡献单元、权重为1，其他贡献单元及权重后续补充
     *
     * @param acceptor
     */
    Donor(Acceptor &acceptor)
        : acceptorID(acceptor.GetAcceptorID()),
          acceptorProcID(acceptor.GetAcceptorProcID()),
          acceptorCenter(acceptor.GetAcceptorCenter()),
          donorIDlist({acceptor.GetCentralDonorID()}),
          donorWeights({1.0}) {};

    /**
     * @brief Destroy the Donor object
     *
     */
    ~Donor() = default;

    // Getters (const版本)
    const List<int> &GetDonorIDlist() const { return this->donorIDlist; }
    const List<Scalar> &GetDonorWeights() const { return this->donorWeights; }
    const int &GetAcceptorID() const { return this->acceptorID; }
    const int &GetAcceptorProcID() const { return this->acceptorProcID; }
    const Vector &GetAcceptorCenter() const { return this->acceptorCenter; }

    // Getters (非const版本，用于插值器修改贡献单元列表和权重)
    List<int> &GetDonorIDlist() { return this->donorIDlist; }
    List<Scalar> &GetDonorWeights() { return this->donorWeights; }

    // Setters
    void SetDonorIDlist(const List<int> &donorIDlist_) { this->donorIDlist = donorIDlist_; }
    void SetDonorWeights(const List<Scalar> &donorWeights_) { this->donorWeights = donorWeights_; }

  private:
    // 贡献单元信息
    List<int> donorIDlist;     // 贡献单元编号列表，第一个元素是插值单元中心所在的单元,称为“中央贡献单元CentralDonor”
    List<Scalar> donorWeights; // 根据插值方法计算的贡献单元插值权重，与donorIDlist中的单元编号一一对应

    // 插值单元信息
    int acceptorID;
    int acceptorProcID;
    Vector acceptorCenter;
  };
} // namespace Floga

#endif